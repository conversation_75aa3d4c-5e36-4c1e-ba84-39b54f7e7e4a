import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { message } from 'antd';
import WebSocketClient, { ConnectionState } from '../../services/WebSocketClient';
import {
  SendOutlined,
  ReloadOutlined,
  DisconnectOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CodeOutlined
} from '@ant-design/icons';

import {
  websocketConnected,
  websocketDisconnected,
  websocketMessageReceived,
  connectWebSocket
} from '../../redux/minimal-store';
import { styled } from '../../design-system';
import { Button, Card, Input } from '../../design-system';
import theme from '../../design-system/theme';

const WebSocketManagerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme?.spacing?.[4] || '16px'};
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme?.spacing?.[2] || '8px'};
  padding: ${theme?.spacing?.[3] || '12px'};
  border-radius: ${theme?.borderRadius?.md || '4px'};
  background-color: ${props => props.connected ? theme?.colors?.success?.light || '#D1FAE5' : theme?.colors?.error?.light || '#FEE2E2'};
  color: ${props => props.connected ? theme?.colors?.success?.dark || '#047857' : theme?.colors?.error?.dark || '#B91C1C'};
`;

const MessageList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
  max-height: 400px;
  overflow-y: auto;
  padding: ${theme.spacing[2]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.neutral[50]};
`;

const Message = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[3]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${props => props.type === 'sent' ? theme.colors.primary.light : 'white'};
  border-left: 4px solid ${props => props.type === 'sent' ? theme.colors.primary.main : theme.colors.secondary.main};
  box-shadow: ${theme.shadows.sm};

  .message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[1]};
    font-size: ${theme.typography.fontSize.sm};
    color: ${theme.colors.neutral[500]};
  }

  .timestamp {
    font-family: monospace;
    font-size: ${theme.typography.fontSize.xs};
    color: ${theme.colors.neutral[600]};
    background-color: ${theme.colors.neutral[100]};
    padding: 2px 4px;
    border-radius: 3px;
  }

  .message-content {
    word-break: break-word;
  }

  pre {
    background-color: ${theme.colors.neutral[100]};
    padding: ${theme.spacing[2]};
    border-radius: ${theme.borderRadius.sm};
    overflow-x: auto;
    font-family: ${theme.typography.fontFamily.code};
    font-size: ${theme.typography.fontSize.sm};
    margin: ${theme.spacing[2]} 0 0 0;
  }
`;

const MessageInput = styled.div`
  display: flex;
  gap: ${theme.spacing[2]};
`;

const SettingsPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[3]};
  padding: ${theme.spacing[3]};
  border: 1px solid ${theme.colors.neutral[200]};
  border-radius: ${theme.borderRadius.md};
  background-color: white;
`;

const SettingsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${theme.colors.neutral[200]};
  margin-bottom: ${theme.spacing[4]};
`;

const Tab = styled.div`
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  cursor: pointer;
  border-bottom: 2px solid ${props => props.active ? theme.colors.primary.main : 'transparent'};
  color: ${props => props.active ? theme.colors.primary.main : theme.colors.neutral[700]};
  font-weight: ${props => props.active ? theme.typography.fontWeight.semibold : theme.typography.fontWeight.medium};

  &:hover {
    color: ${theme.colors.primary.main};
  }
`;

const MessageTemplates = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${theme.spacing[2]};
  margin-bottom: ${theme.spacing[3]};
`;

const EnhancedWebSocketManager = () => {
  const dispatch = useDispatch();
  const { connected, messages, url } = useSelector(state => state.websocket);

  const [message, setMessage] = useState('');
  const [activeTab, setActiveTab] = useState('messages');
  const [wsUrl, setWsUrl] = useState(url || 'ws://localhost:8000/ws/test/');
  const [autoReconnect, setAutoReconnect] = useState(true);
  const [showTimestamp, setShowTimestamp] = useState(true);
  const [localMessages, setLocalMessages] = useState([]);
  // WebSocket client instance
  const [wsClient, setWsClient] = useState(null);

  const messagesEndRef = useRef(null);

  // Log the WebSocket URL for debugging
  useEffect(() => {
    console.log('WebSocketManager: Using WebSocket URL:', wsUrl);
  }, [wsUrl]);

  // Auto-connect when the component mounts if autoReconnect is true
  useEffect(() => {
    let isMounted = true; // Flag to track if component is mounted

    if (autoReconnect && !connected && !wsClient) {
      console.log('Auto-connecting to WebSocket...');
      // Use setTimeout to avoid potential timing issues
      const timer = setTimeout(() => {
        // Only proceed if component is still mounted
        if (isMounted) {
          handleConnect();
        }
      }, 100);

      // Cleanup function
      return () => {
        isMounted = false; // Set flag to false when component unmounts
        clearTimeout(timer); // Clear the timeout
      };
    }
  }, [autoReconnect, connected, wsClient, handleConnect]); // Include handleConnect in dependencies

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Define event handlers as memoized functions to prevent recreation on each render
  const handleOpen = useCallback((event) => {
    console.log('WebSocket connected:', event);
    // Update Redux state
    dispatch(websocketConnected());
    // Show success message
    message.success('WebSocket connected successfully');
  }, [dispatch]);

  const handleClose = useCallback((event) => {
    console.log('WebSocket disconnected:', event);
    // Update Redux state
    dispatch(websocketDisconnected());
    // Show error message if not a normal closure
    if (event.code !== 1000) {
      message.warning(`WebSocket disconnected: ${event.code} ${event.reason}`);
    }
  }, [dispatch]);

  const handleError = useCallback((error) => {
    console.error('WebSocket error:', error);

    // Log stack trace if available
    if (error && error.stack) {
      console.error('WebSocket error stack trace:', error.stack);
    } else {
      // Capture a new stack trace to see where the error is being handled
      const newError = new Error('WebSocket error stack trace');
      console.error('WebSocket error handler stack trace:', newError.stack);
    }

    message.error('WebSocket error: ' + (error.message || 'Unknown error'));
  }, []);

  const handleMessage = useCallback((data) => {
    console.log('WebSocket message received:', data);
    dispatch(websocketMessageReceived(data));
  }, [dispatch]);

  // Cleanup WebSocket connection when component unmounts
  useEffect(() => {
    // No setup code needed, just return the cleanup function
    return () => {
      // Create a local variable to capture the current wsClient value
      const currentClient = wsClient;

      if (currentClient) {
        console.log('Cleaning up WebSocket connection on component unmount');

        // Set a flag to indicate that the component is unmounting
        // This prevents any pending callbacks from updating state
        currentClient.isComponentUnmounted = true;

        // Clean up event listeners if they exist
        if (currentClient.eventHandlers) {
          Object.entries(currentClient.eventHandlers).forEach(([event, handler]) => {
            try {
              currentClient.removeEventListener(event, handler);
            } catch (error) {
              console.error(`Error removing ${event} event listener:`, error);
            }
          });
        }

        // Remove any one-time listeners that might have been added
        try {
          // These are the one-time listeners we added in handleConnect
          currentClient.removeEventListener('open', currentClient._connectionSuccessHandler);
          currentClient.removeEventListener('error', currentClient._connectionFailureHandler);
        } catch (error) {
          // Ignore errors, the listeners might not exist
        }

        // Close the connection
        try {
          currentClient.close(1000, 'Component unmounted');
        } catch (error) {
          console.error('Error closing WebSocket connection:', error);
        }
      }
    };
  }, [wsClient]); // Dependency on wsClient to ensure we have the latest reference

  const handleConnect = useCallback(() => {
    // Connect to WebSocket using WebSocketClient
    try {
      console.log('handleConnect called');
      console.log('Current state - wsUrl:', wsUrl, 'wsClient:', wsClient, 'connected:', connected);
      console.log('this in handleConnect:', this); // Note: 'this' will be undefined in a functional component

      // Check if we already have a client with the same URL
      if (wsClient && wsClient.url === wsUrl) {
        console.log('Existing client found with matching URL');
        console.log('Client state:', wsClient.connectionState);
        console.log('ConnectionState.OPEN:', ConnectionState.OPEN);

        // If the client exists and has the same URL, just reconnect if not connected
        if (wsClient.connectionState !== ConnectionState.OPEN) {
          console.log('Reconnecting existing WebSocketClient');
          console.log('Before open call - wsClient:', wsClient);
          wsClient.open();
          console.log('After open call - wsClient:', wsClient);
          return;
        } else {
          console.log('WebSocketClient already connected to', wsUrl);
          return;
        }
      }

      // Clean up existing client if any
      if (wsClient) {
        console.log('Closing existing WebSocketClient with URL:', wsClient.url);
        wsClient.close();
      }

      // Create a new WebSocketClient instance with proper configuration
      console.log('Creating new WebSocketClient with URL:', wsUrl);

      const clientOptions = {
        url: wsUrl,
        debug: true,
        reconnectInterval: 1000,
        maxReconnectAttempts: 5,
        connectionTimeout: 5000 // 5 second connection timeout
      };

      console.log('WebSocketClient options:', clientOptions);

      const client = new WebSocketClient(clientOptions);

      console.log('WebSocketClient instance created:', client);
      console.log('Client properties - url:', client.url, 'connectionState:', client.connectionState);

      // Use the memoized event handlers defined at the component level
      console.log('Event handlers before binding:');
      console.log('handleOpen:', handleOpen);
      console.log('handleClose:', handleClose);
      console.log('handleError:', handleError);
      console.log('handleMessage:', handleMessage);

      // Add event listeners
      console.log('Adding event listeners to client...');
      client.addEventListener('open', handleOpen);
      client.addEventListener('close', handleClose);
      client.addEventListener('error', handleError);
      client.addEventListener('message', handleMessage);
      console.log('Event listeners added');

      // Store event handlers on the client for later cleanup
      console.log('Storing event handlers on client for later cleanup');
      client.eventHandlers = {
        open: handleOpen,
        close: handleClose,
        error: handleError,
        message: handleMessage
      };
      console.log('Event handlers stored on client:', client.eventHandlers);

      // Create a flag to track if the component is still mounted
      let isMounted = true;

      // Add a one-time listener for connection success
      console.log('Creating one-time connection success handler');
      const connectionSuccessHandler = () => {
        console.log('connectionSuccessHandler called');
        console.log('isMounted:', isMounted, 'client.isComponentUnmounted:', client.isComponentUnmounted);

        // Only update state if component is still mounted
        if (isMounted && !client.isComponentUnmounted) {
          console.log('WebSocketClient connected successfully');
          console.log('Before setWsClient - current wsClient:', wsClient);
          // Update state with the connected client
          setWsClient(client);
          console.log('After setWsClient call');
        } else {
          console.log('Component unmounted or marked for unmounting, not updating state');
        }
      };

      // Add a one-time listener for connection failure
      console.log('Creating one-time connection failure handler');
      const connectionFailureHandler = (error) => {
        console.log('connectionFailureHandler called with error:', error);
        console.log('isMounted:', isMounted, 'client.isComponentUnmounted:', client.isComponentUnmounted);

        console.error('WebSocketClient connection failed:', error);
        // Only clean up if component is still mounted
        if (isMounted && !client.isComponentUnmounted) {
          console.log('Cleaning up client after connection failure');
          // Clean up the client if connection fails
          client.close();
          console.log('Client closed after connection failure');
          // Don't update state with the failed client
        } else {
          console.log('Component unmounted or marked for unmounting, not cleaning up');
        }
      };

      // Store references to the one-time listeners for later cleanup
      console.log('Storing references to one-time listeners');
      client._connectionSuccessHandler = connectionSuccessHandler;
      client._connectionFailureHandler = connectionFailureHandler;
      console.log('References stored - client._connectionSuccessHandler:', !!client._connectionSuccessHandler);

      // Add the one-time listeners
      console.log('Adding one-time listeners for open and error events');
      client.addEventListener('open', connectionSuccessHandler, { once: true });
      client.addEventListener('error', connectionFailureHandler, { once: true });
      console.log('One-time listeners added');

      // Open the connection
      console.log('Opening WebSocket connection...');
      client.open();
      console.log('WebSocket open method called');

      // Save the client instance immediately so we can reference it for cleanup
      console.log('Saving client instance to state - current wsClient:', wsClient);
      setWsClient(client);
      console.log('Client instance saved to state');

      console.log('WebSocket connection initiated');
    } catch (error) {
      console.error('WebSocket connection error:', error);

      // Log stack trace if available
      if (error && error.stack) {
        console.error('WebSocket connection error stack trace:', error.stack);
      } else {
        // Capture a new stack trace to see where the error is being handled
        const newError = new Error('WebSocket connection error stack trace');
        console.error('WebSocket connection error handler stack trace:', newError.stack);
      }

      message.error('Failed to connect to WebSocket: ' + error.message);
    }
  }, [wsUrl, wsClient, dispatch, handleOpen, handleClose, handleError, handleMessage]);

  const handleDisconnect = useCallback(() => {
    try {
      // Disconnect using the WebSocketClient instance
      if (wsClient) {
        // Clean up event listeners if they exist
        if (wsClient.eventHandlers) {
          Object.entries(wsClient.eventHandlers).forEach(([event, handler]) => {
            wsClient.removeEventListener(event, handler);
          });
        }

        // Close the connection
        wsClient.close(1000, 'User initiated disconnect');
        setWsClient(null);

        // Update Redux state
        dispatch(websocketDisconnected());

        // Show success message
        message.success('WebSocket disconnected');
      } else {
        message.warning('WebSocket is not connected');
      }
    } catch (error) {
      console.error('WebSocket disconnection error:', error);

      // Log stack trace if available
      if (error && error.stack) {
        console.error('WebSocket disconnection error stack trace:', error.stack);
      } else {
        // Capture a new stack trace to see where the error is being handled
        const newError = new Error('WebSocket disconnection error stack trace');
        console.error('WebSocket disconnection error handler stack trace:', newError.stack);
      }

      message.error('Failed to disconnect from WebSocket: ' + error.message);
    }
  }, [wsClient, dispatch]);

  const handleSendMessage = useCallback(() => {
    if (!message.trim() || !connected || !wsClient) return;

    try {
      // Try to parse as JSON if it looks like JSON
      let messageData;
      if (message.trim().startsWith('{') || message.trim().startsWith('[')) {
        messageData = JSON.parse(message);
      } else {
        messageData = { message: message };
      }

      // Create a timestamp in a consistent format
      const now = new Date();
      const timestamp = now.toISOString();

      // Log the timestamp for debugging
      console.log('Sending message with timestamp:', timestamp);

      // Add timestamp to the message data
      messageData.timestamp = timestamp;

      // Send the message through the WebSocket
      wsClient.send(messageData);

      // Add to the message list
      dispatch(websocketMessageReceived({
        type: 'sent',
        content: messageData,
        timestamp: timestamp
      }));

      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);

      // Log stack trace if available
      if (error && error.stack) {
        console.error('WebSocket send message error stack trace:', error.stack);
      } else {
        // Capture a new stack trace to see where the error is being handled
        const newError = new Error('WebSocket send message error stack trace');
        console.error('WebSocket send message error handler stack trace:', newError.stack);
      }

      message.error(`Error sending message: ${error.message}`);
    }
  }, [message, connected, wsClient, dispatch, setMessage]);

  const handleClearMessages = useCallback(() => {
    // In a real implementation, this would clear the message list in the store
    // For now, we'll just set the messages array to empty in the component state
    setLocalMessages([]);
  }, [setLocalMessages]);

  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const formatTimestamp = useCallback((timestamp) => {
    if (!showTimestamp) return null;

    try {
      // Parse the timestamp string to a Date object
      const date = new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return null;
      }

      // Format the date with hours, minutes, seconds, and milliseconds
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return null;
    }
  }, [showTimestamp]);

  const isJsonString = useCallback((str) => {
    try {
      const json = JSON.parse(str);
      return typeof json === 'object';
    } catch (e) {
      return false;
    }
  }, []);

  const renderMessageContent = useCallback((content) => {
    if (typeof content === 'string' && isJsonString(content)) {
      return (
        <>
          <div className="message-content">JSON Message</div>
          <pre>{JSON.stringify(JSON.parse(content), null, 2)}</pre>
        </>
      );
    } else if (typeof content === 'object') {
      return (
        <>
          <div className="message-content">Object Message</div>
          <pre>{JSON.stringify(content, null, 2)}</pre>
        </>
      );
    } else {
      return <div className="message-content">{content}</div>;
    }
  }, [isJsonString]);

  const messageTemplates = [
    { name: 'Ping', content: '{"type": "ping"}' },
    { name: 'Request Data', content: '{"type": "request_data"}' },
    { name: 'Hello', content: '{"type": "message", "content": "Hello, WebSocket!"}' },
    { name: 'Status', content: '{"type": "status_request"}' }
  ];

  return (
    <WebSocketManagerContainer>
      <TabContainer>
        <Tab
          active={activeTab === 'messages'}
          onClick={() => setActiveTab('messages')}
        >
          Messages
        </Tab>
        <Tab
          active={activeTab === 'settings'}
          onClick={() => setActiveTab('settings')}
        >
          Settings
        </Tab>
        <Tab
          active={activeTab === 'info'}
          onClick={() => setActiveTab('info')}
        >
          Info
        </Tab>
      </TabContainer>

      {activeTab === 'messages' && (
        <>
          <Card>
            <Card.Header>
              <Card.Title>WebSocket Connection</Card.Title>
              <div>
                {connected ? (
                  <Button
                    variant="outline"
                    size="small"
                    onClick={handleDisconnect}
                    startIcon={<DisconnectOutlined />}
                    style={{ color: theme.colors.error.main, borderColor: theme.colors.error.main }}
                  >
                    Disconnect
                  </Button>
                ) : (
                  <Button
                    variant="primary"
                    size="small"
                    onClick={handleConnect}
                    startIcon={<ReloadOutlined />}
                  >
                    Connect
                  </Button>
                )}
              </div>
            </Card.Header>
            <Card.Content>
              <ConnectionStatus connected={connected}>
                {connected ? (
                  <>
                    <CheckCircleOutlined />
                    <span>Connected to {wsUrl}</span>
                  </>
                ) : (
                  <>
                    <CloseCircleOutlined />
                    <span>Disconnected</span>
                  </>
                )}
              </ConnectionStatus>
            </Card.Content>
          </Card>

          <Card>
            <Card.Header>
              <Card.Title>Messages</Card.Title>
              <Button
                variant="text"
                size="small"
                onClick={handleClearMessages}
                startIcon={<ClearOutlined />}
              >
                Clear
              </Button>
            </Card.Header>
            <Card.Content>
              <MessageTemplates>
                <div style={{ marginRight: theme.spacing[2], display: 'flex', alignItems: 'center' }}>
                  Templates:
                </div>
                {messageTemplates.map((template, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="small"
                    onClick={() => setMessage(template.content)}
                  >
                    {template.name}
                  </Button>
                ))}
              </MessageTemplates>

              <MessageInput>
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={connected ? "Type a message..." : "Connect to send messages..."}
                  disabled={!connected}
                  fullWidth
                  as="textarea"
                  rows={3}
                />
                <Button
                  variant="primary"
                  onClick={handleSendMessage}
                  disabled={!connected || !message.trim()}
                  style={{ alignSelf: 'flex-end' }}
                >
                  <SendOutlined />
                </Button>
              </MessageInput>

              <div style={{ margin: `${theme.spacing[3]} 0` }}>
                <MessageList>
                  {messages.length === 0 ? (
                    <div style={{
                      padding: theme.spacing[4],
                      textAlign: 'center',
                      color: theme.colors.neutral[500]
                    }}>
                      No messages yet
                    </div>
                  ) : (
                    messages.map((msg, index) => (
                      <Message key={index} type={msg.type}>
                        <div className="message-header">
                          <span>{msg.type === 'sent' ? 'Sent' : 'Received'}</span>
                          <span className="timestamp">{formatTimestamp(msg.timestamp) || 'Unknown time'}</span>
                        </div>
                        {renderMessageContent(msg.content)}
                      </Message>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </MessageList>
              </div>
            </Card.Content>
          </Card>
        </>
      )}

      {activeTab === 'settings' && (
        <Card>
          <Card.Header>
            <Card.Title>WebSocket Settings</Card.Title>
            <SettingOutlined />
          </Card.Header>
          <Card.Content>
            <SettingsPanel>
              <SettingsGroup>
                <label>WebSocket URL</label>
                <Input
                  value={wsUrl}
                  onChange={(e) => setWsUrl(e.target.value)}
                  placeholder="ws://localhost:8000/ws/"
                  fullWidth
                />
              </SettingsGroup>

              <SettingsGroup>
                <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
                  <input
                    type="checkbox"
                    id="auto-reconnect"
                    checked={autoReconnect}
                    onChange={(e) => setAutoReconnect(e.target.checked)}
                  />
                  <label htmlFor="auto-reconnect">Auto-reconnect on disconnect</label>
                </div>
              </SettingsGroup>

              <SettingsGroup>
                <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[2] }}>
                  <input
                    type="checkbox"
                    id="show-timestamp"
                    checked={showTimestamp}
                    onChange={(e) => setShowTimestamp(e.target.checked)}
                  />
                  <label htmlFor="show-timestamp">Show message timestamps</label>
                </div>
              </SettingsGroup>

              <div style={{ marginTop: theme.spacing[2] }}>
                <Button
                  variant="primary"
                  onClick={handleConnect}
                  startIcon={<ReloadOutlined />}
                >
                  Apply Settings & Connect
                </Button>
              </div>
            </SettingsPanel>
          </Card.Content>
        </Card>
      )}

      {activeTab === 'info' && (
        <Card>
          <Card.Header>
            <Card.Title>WebSocket Information</Card.Title>
            <InfoCircleOutlined />
          </Card.Header>
          <Card.Content>
            <div style={{ display: 'flex', flexDirection: 'column', gap: theme.spacing[4] }}>
              <div>
                <h3>About WebSockets</h3>
                <p>
                  WebSocket is a communication protocol that provides full-duplex communication channels over a single TCP connection.
                  It enables real-time, bidirectional communication between a client and server.
                </p>
              </div>

              <div>
                <h3>Usage Examples</h3>
                <div style={{
                  backgroundColor: theme.colors.neutral[100],
                  padding: theme.spacing[3],
                  borderRadius: theme.borderRadius.md,
                  fontFamily: theme.typography.fontFamily.code
                }}>
                  <div style={{ marginBottom: theme.spacing[2] }}>
                    <strong>Ping Message:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"ping\"\n}"}</pre>
                  </div>

                  <div style={{ marginBottom: theme.spacing[2] }}>
                    <strong>Request Data:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"request_data\"\n}"}</pre>
                  </div>

                  <div>
                    <strong>Send Message:</strong>
                    <pre style={{ margin: theme.spacing[1] }}>{"{\n  \"type\": \"message\",\n  \"content\": \"Hello, WebSocket!\"\n}"}</pre>
                  </div>
                </div>
              </div>

              <div>
                <h3>Troubleshooting</h3>
                <ul style={{ paddingLeft: theme.spacing[4] }}>
                  <li>Make sure the WebSocket server is running</li>
                  <li>Check that the WebSocket URL is correct</li>
                  <li>Ensure there are no network restrictions blocking WebSocket connections</li>
                  <li>Check browser console for any connection errors</li>
                </ul>
              </div>
            </div>
          </Card.Content>
        </Card>
      )}
    </WebSocketManagerContainer>
  );
};

export default EnhancedWebSocketManager;
