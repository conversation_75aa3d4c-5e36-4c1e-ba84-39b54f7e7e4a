"""
Authentication views for the App Builder API.
"""
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from .jwt_auth import generate_jwt_token
from ..error_handling import error_response, handle_exception
from ..security import add_security_headers, sanitize_request, get_sanitized_data
from ..rate_limiting import rate_limit
import logging

# Set up logger
logger = logging.getLogger(__name__)

@api_view(['POST'])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=5, period=60, scope='ip')  # 5 requests per minute per IP
def login(request):
    """
    Login a user and return a JWT token.
    """
    username = get_sanitized_data(request, 'username')
    password = get_sanitized_data(request, 'password')

    # Validate the request
    if not username or not password:
        return error_response(
            'INVALID_CREDENTIALS',
            'Username and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Authenticate the user
    user = authenticate(username=username, password=password)
    if not user:
        logger.warning(f"Failed login attempt for user: {username}")
        return error_response(
            'INVALID_CREDENTIALS',
            'Invalid username or password',
            status.HTTP_401_UNAUTHORIZED
        )

    # Generate a JWT token
    token = generate_jwt_token(user)

    # Return the token
    return Response({
        'token': token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    })


@api_view(['POST'])
@permission_classes([AllowAny])
@handle_exception
@add_security_headers
@sanitize_request
@rate_limit(requests=3, period=3600, scope='ip')  # 3 requests per hour per IP
def register(request):
    """
    Register a new user.
    """
    username = get_sanitized_data(request, 'username')
    email = get_sanitized_data(request, 'email')
    password = get_sanitized_data(request, 'password')
    first_name = get_sanitized_data(request, 'first_name', '')
    last_name = get_sanitized_data(request, 'last_name', '')

    # Validate the request
    if not username or not email or not password:
        return error_response(
            'INVALID_REQUEST',
            'Username, email, and password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if the username already exists
    if User.objects.filter(username=username).exists():
        return error_response(
            'USERNAME_EXISTS',
            'Username already exists',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if the email already exists
    if User.objects.filter(email=email).exists():
        return error_response(
            'EMAIL_EXISTS',
            'Email already exists',
            status.HTTP_400_BAD_REQUEST
        )

    # Create the user
    user = User.objects.create_user(
        username=username,
        email=email,
        password=password,
        first_name=first_name,
        last_name=last_name
    )

    # Generate a JWT token
    token = generate_jwt_token(user)

    # Return the token
    return Response({
        'token': token,
        'user': {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_staff': user.is_staff
        }
    }, status=status.HTTP_201_CREATED)


@api_view(['GET'])
@handle_exception
@add_security_headers
def user_profile(request):
    """
    Get the user profile.
    """
    user = request.user

    # Return the user profile
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'date_joined': user.date_joined,
        'last_login': user.last_login
    })


@api_view(['PUT'])
@handle_exception
@add_security_headers
@sanitize_request
def update_profile(request):
    """
    Update the user profile.
    """
    user = request.user
    first_name = request.data.get('first_name')
    last_name = request.data.get('last_name')
    email = request.data.get('email')

    # Update the user profile
    if first_name is not None:
        user.first_name = first_name
    if last_name is not None:
        user.last_name = last_name
    if email is not None:
        # Check if the email already exists
        if User.objects.filter(email=email).exclude(id=user.id).exists():
            return error_response(
                'EMAIL_EXISTS',
                'Email already exists',
                status.HTTP_400_BAD_REQUEST
            )
        user.email = email

    # Save the user
    user.save()

    # Return the updated user profile
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'date_joined': user.date_joined,
        'last_login': user.last_login
    })


@api_view(['POST'])
@handle_exception
@add_security_headers
@sanitize_request
def change_password(request):
    """
    Change the user password.
    """
    user = request.user
    current_password = request.data.get('current_password')
    new_password = request.data.get('new_password')

    # Validate the request
    if not current_password or not new_password:
        return error_response(
            'INVALID_REQUEST',
            'Current password and new password are required',
            status.HTTP_400_BAD_REQUEST
        )

    # Check if the current password is correct
    if not user.check_password(current_password):
        return error_response(
            'INVALID_PASSWORD',
            'Current password is incorrect',
            status.HTTP_400_BAD_REQUEST
        )

    # Change the password
    user.set_password(new_password)
    user.save()

    # Return success
    return Response({
        'message': 'Password changed successfully'
    })
