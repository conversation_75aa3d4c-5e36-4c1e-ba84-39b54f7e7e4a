<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .success {
            color: green;
            font-weight: bold;
        }

        .error {
            color: red;
            font-weight: bold;
        }

        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }

        button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #45a049;
        }
    </style>
</head>

<body>
    <h1>Server Test Page</h1>
    <p>This is a simple test page to verify that the server is working.</p>

    <div>
        <h2>Server Status</h2>
        <div id="server-status">Checking...</div>
        <button onclick="checkBackend()">Check Backend</button>
        <button onclick="window.location.reload()">Reload Page</button>
    </div>

    <div>
        <h2>Network Information</h2>
        <pre id="network-info">Loading...</pre>
    </div>

    <script>
        // Display network information
        function updateNetworkInfo() {
            const info = {
                online: navigator.onLine,
                userAgent: navigator.userAgent,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host
            };

            document.getElementById('network-info').textContent = JSON.stringify(info, null, 2);
        }

        // Check backend connection
        function checkBackend() {
            const statusElement = document.getElementById('server-status');
            statusElement.innerHTML = 'Checking backend connection...';
            statusElement.className = '';

            fetch('/api/health')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error(`Server responded with status: ${response.status}`);
                })
                .then(data => {
                    statusElement.textContent = `Backend is online! Response: ${JSON.stringify(data)}`;
                    statusElement.className = 'success';
                })
                .catch(error => {
                    statusElement.textContent = `Backend connection failed: ${error.message}`;
                    statusElement.className = 'error';
                    console.error('Backend check error:', error);
                });
        }

        // Initialize
        updateNetworkInfo();
        window.addEventListener('online', updateNetworkInfo);
        window.addEventListener('offline', updateNetworkInfo);

        // Check backend on load
        setTimeout(checkBackend, 1000);
    </script>
</body>

</html>