import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { ThemeProvider as StyledThemeProvider, createGlobalStyle } from 'styled-components';
import theme, { lightTheme, darkTheme, blueTheme, highContrastTheme, colorPalette, fontWeights } from '../styles/theme';
import { useUserPreferences } from '../contexts/UserPreferencesContext';

// Define default themes for fallback
const defaultThemes = ['light', 'dark', 'blue', 'high-contrast'];

const GlobalStyle = createGlobalStyle`
  body {
    font-family: ${props => props.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'};
    background-color: ${props => props.backgroundColor || props.theme?.colorPalette?.background || '#FFFFFF'};
    color: ${props => props.textColor || props.theme?.colorPalette?.textPrimary || '#111827'};
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  *, *:before, *:after {
    box-sizing: inherit;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    font-weight: ${fontWeights.semibold};
    color: ${props => props.textColor || props.theme?.colorPalette?.textPrimary || '#111827'};
    transition: color 0.3s ease;
  }

  a {
    color: ${props => props.primaryColor || props.theme?.colorPalette?.primary || '#2563EB'};
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      text-decoration: underline;
    }
  }

  button {
    font-family: ${props => props.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'};
  }

  .ant-layout {
    background-color: ${props => props.theme?.colorPalette?.background || '#FFFFFF'};
    transition: background-color 0.3s ease;
  }

  .ant-layout-header {
    background-color: ${props => props.theme?.colorPalette?.backgroundSecondary || '#F9FAFB'};
    transition: background-color 0.3s ease;
  }

  .ant-layout-footer {
    background-color: ${props => props.theme?.colorPalette?.backgroundSecondary || '#F9FAFB'};
    transition: background-color 0.3s ease;
  }

  .ant-layout-content {
    background-color: ${props => props.theme?.colorPalette?.backgroundTertiary || '#F3F4F6'};
    transition: background-color 0.3s ease;
  }

  .ant-card {
    background-color: ${props => props.theme?.colorPalette?.backgroundSecondary || '#FFFFFF'};
    transition: background-color 0.3s ease;
  }

  .ant-menu {
    background-color: transparent;
    color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
    transition: color 0.3s ease;
  }
`;

// Create a context for theme switching
export const ThemeContext = createContext({
  themeMode: 'light',
  setThemeMode: () => { },
  toggleTheme: () => { },
  availableThemes: defaultThemes,
});

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const ThemeProvider = ({ children }) => {
  // Available themes
  const availableThemes = defaultThemes;

  // Try to get user preferences using the hook
  let userPreferences;
  try {
    userPreferences = useUserPreferences();
  } catch (error) {
    // If the hook fails (context not available), userPreferences will be undefined
    console.log('UserPreferences context not available, using localStorage fallback');
  }

  // Get the initial theme from user preferences, localStorage, or default to light
  const [themeMode, setThemeMode] = useState(() => {
    // If UserPreferencesProvider is available, use its theme
    if (userPreferences && userPreferences.preferences) {
      return userPreferences.preferences.theme;
    }

    // Otherwise fall back to localStorage
    const savedTheme = localStorage.getItem('themeMode');
    return availableThemes.includes(savedTheme) ? savedTheme : 'light';
  });

  // Set theme function
  const handleSetThemeMode = (mode) => {
    if (availableThemes.includes(mode)) {
      // Update local state
      setThemeMode(mode);

      // Update localStorage for backward compatibility
      localStorage.setItem('themeMode', mode);

      // Update user preferences if available
      if (userPreferences && userPreferences.setPreference) {
        userPreferences.setPreference('theme', mode);
      }
    }
  };

  // Toggle theme function (cycles through themes)
  const toggleTheme = () => {
    setThemeMode(prevMode => {
      const currentIndex = availableThemes.indexOf(prevMode);
      const nextIndex = (currentIndex + 1) % availableThemes.length;
      const newMode = availableThemes[nextIndex];
      localStorage.setItem('themeMode', newMode);
      return newMode;
    });
  };

  // Get the active theme from Redux store (for backward compatibility)
  const themes = useSelector(state => {
    // Check if themes slice exists in the state
    if (!state || !state.themes) {
      console.warn('Redux state or themes slice not found, using fallback values');
      return [];
    }

    // If themes is an array, use it directly
    if (Array.isArray(state.themes)) {
      return state.themes;
    }

    // If themes is an object with a themes property (as in themeReducer.js)
    if (state.themes.themes && Array.isArray(state.themes.themes)) {
      return state.themes.themes;
    }

    // Fallback to empty array
    return [];
  });

  const activeThemeId = useSelector(state => {
    // Check if themes slice exists in the state
    if (!state || !state.themes) {
      console.warn('Redux state or themes slice not found, using fallback values for activeTheme');
      return null;
    }

    // If activeTheme is at the root of the themes slice
    if (state.themes.activeTheme) {
      return state.themes.activeTheme;
    }

    // Fallback to null
    return null;
  });

  // Select the theme based on the theme mode
  const getTheme = (mode) => {
    switch (mode) {
      case 'dark':
        return darkTheme;
      case 'blue':
        return blueTheme;
      case 'high-contrast':
        return highContrastTheme;
      case 'light':
      default:
        return lightTheme;
    }
  };

  // Try to get theme from Redux first if activeThemeId is available
  let currentTheme;
  if (activeThemeId && themes.length > 0) {
    // Find the theme in the themes array
    const reduxTheme = themes.find(t => t.id === activeThemeId);
    if (reduxTheme) {
      try {
        // If the Redux theme has the necessary properties, convert it to a format compatible with our theme system
        // This assumes the Redux theme has properties like primaryColor, backgroundColor, etc.
        currentTheme = {
          ...getTheme(themeMode), // Start with a base theme
          colorPalette: {
            ...getTheme(themeMode).colorPalette,
            primary: reduxTheme.primaryColor || getTheme(themeMode).colorPalette.primary,
            secondary: reduxTheme.secondaryColor || getTheme(themeMode).colorPalette.secondary,
            background: reduxTheme.backgroundColor || getTheme(themeMode).colorPalette.background,
            textPrimary: reduxTheme.textColor || getTheme(themeMode).colorPalette.textPrimary
          },
          fonts: {
            ...getTheme(themeMode).fonts,
            body: reduxTheme.fontFamily || getTheme(themeMode).fonts.body
          }
        };
      } catch (error) {
        console.error('Error converting Redux theme to theme format:', error);
        // Fall back to the theme mode if there's an error
        currentTheme = getTheme(themeMode);
      }
    } else {
      // If not found, fall back to the theme mode
      currentTheme = getTheme(themeMode);
    }
  } else {
    // If no Redux theme is available, use the theme mode
    currentTheme = getTheme(themeMode);
  }

  // Apply theme to document body
  useEffect(() => {
    document.body.dataset.theme = themeMode;
  }, [themeMode]);

  // Separate effect to sync with user preferences if they change
  useEffect(() => {
    if (userPreferences && userPreferences.preferences && userPreferences.preferences.theme !== themeMode) {
      setThemeMode(userPreferences.preferences.theme);
    }
    // Only run when userPreferences change, not when themeMode changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userPreferences?.preferences?.theme]);

  return (
    <ThemeContext.Provider value={{
      themeMode,
      setThemeMode: handleSetThemeMode,
      toggleTheme,
      availableThemes
    }}>
      <StyledThemeProvider theme={currentTheme}>
        <GlobalStyle />
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};

// Make sure ThemeProvider is properly exported
export { ThemeProvider };
export default ThemeProvider;
