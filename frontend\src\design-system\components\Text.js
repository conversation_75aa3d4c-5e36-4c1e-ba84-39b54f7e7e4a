import styled from 'styled-components';

// Create the base Text component
const Text = styled.span`
  font-family: ${props => props.theme?.typography?.fontFamily || 'Inter, sans-serif'};
  font-size: ${props => props.theme?.typography?.fontSize?.[props.size || 'md'] || '16px'};
  font-weight: ${props => props.theme?.typography?.fontWeight?.[props.weight || 'normal'] || 400};
  color: ${props => props.color ? props.theme?.colors?.[props.color] || props.color : props.theme?.colors?.text?.primary || '#111827'};
  line-height: ${props => props.theme?.typography?.lineHeight?.[props.lineHeight || 'normal'] || 1.5};
  margin: 0;
  ${props => props.truncate && `
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  `}
`;

// Create variants using the styled function instead of withComponent
const Paragraph = styled(Text)`
  display: block;
  margin-bottom: ${props => props.theme.spacing[3]};
`;

const Heading = styled(Text)`
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  line-height: ${props => props.theme.typography.lineHeight.tight};
  margin-bottom: ${props => props.theme.spacing[3]};
`;

// Create heading levels
const H1 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.xxxl};
`;

const H2 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.xxl};
`;

const H3 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.xl};
`;

const H4 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.lg};
`;

const H5 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.md};
`;

const H6 = styled(Heading)`
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

// Export all components
export { Text, Paragraph, H1, H2, H3, H4, H5, H6 };
export default Text;