import styled, { css } from 'styled-components';
import { <PERSON>ton, Card, Input, Typography, Alert, Spin, Tag } from 'antd';

/**
 * Reusable styled components for the application
 * These components are built on top of Ant Design components
 * and styled according to the application's design system
 */

const { Title, Text, Paragraph } = Typography;

// Common styles
export const flexCenter = css`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const flexBetween = css`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const flexColumn = css`
  display: flex;
  flex-direction: column;
`;

export const gridLayout = css`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: ${props => props.theme?.spacing?.md || 16}px;
`;

// Typography
export const PageTitle = styled(Title)`
  margin-bottom: ${props => props.theme?.spacing?.lg || 24}px;
  color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
`;

export const SectionTitle = styled(Title)`
  margin-bottom: ${props => props.theme?.spacing?.md || 16}px;
  color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
`;

export const SubTitle = styled(Title)`
  margin-bottom: ${props => props.theme?.spacing?.sm || 12}px;
  color: ${props => props.theme?.colorPalette?.textSecondary || '#4B5563'};
`;

export const HighlightedText = styled(Text)`
  background-color: ${props => props.theme?.colorPalette?.primaryLight || '#DBEAFE'};
  padding: ${props => props.theme?.spacing?.xs || 8}px ${props => props.theme?.spacing?.sm || 12}px;
  border-radius: ${props => props.theme?.borderRadius?.sm || '2px'};
`;

export const CodeText = styled(Text)`
  font-family: 'Courier New', Courier, monospace;
  background-color: ${props => props.theme?.colorPalette?.gray100 || '#F3F4F6'};
  padding: ${props => props.theme?.spacing?.xs || 8}px ${props => props.theme?.spacing?.sm || 12}px;
  border-radius: ${props => props.theme?.borderRadius?.sm || '2px'};
`;

// Layout
export const Container = styled.div`
  max-width: ${props => props.maxWidth || '1200px'};
  margin: 0 auto;
  padding: ${props => props.theme?.spacing?.md || 16}px;
`;

export const Section = styled.section`
  margin-bottom: ${props => props.theme?.spacing?.xl || 32}px;
`;

export const Row = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: -${props => props.theme?.spacing?.sm || 12}px;

  & > * {
    padding: ${props => props.theme?.spacing?.sm || 12}px;
  }
`;

export const Column = styled.div`
  flex: ${props => props.flex || '1'};

  ${props => props.theme?.media?.md || '@media (min-width: 768px)'} {
    flex: ${props => props.flexMd || props.flex || '1'};
  }

  ${props => props.theme?.media?.lg || '@media (min-width: 992px)'} {
    flex: ${props => props.flexLg || props.flexMd || props.flex || '1'};
  }
`;

export const Grid = styled.div`
  ${gridLayout}
`;

// Cards
export const StyledCard = styled(Card)`
  border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
  box-shadow: ${props => props.theme?.shadows?.sm || '0 1px 2px rgba(0, 0, 0, 0.05)'};
  margin-bottom: ${props => props.theme?.spacing?.md || 16}px;
  background-color: ${props => props.theme?.colorPalette?.backgroundSecondary || '#FFFFFF'};

  .ant-card-head {
    border-bottom: 1px solid ${props => props.theme?.colorPalette?.border || '#D1D5DB'};
  }

  .ant-card-head-title {
    color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
  }

  .ant-card-body {
    color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
  }
`;

export const FeatureCard = styled(StyledCard)`
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${props => props.theme?.shadows?.md || '0 4px 6px rgba(0, 0, 0, 0.1)'};
  }
`;

export const DashboardCard = styled(StyledCard)`
  .ant-card-head {
    background-color: ${props => props.theme?.colorPalette?.primaryLight || '#DBEAFE'};
  }
`;

// Buttons
export const PrimaryButton = styled(Button)`
  &.ant-btn-primary {
    background-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
    border-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};

    &:hover, &:focus {
      background-color: ${props => props.theme?.colorPalette?.primaryDark || '#1E40AF'};
      border-color: ${props => props.theme?.colorPalette?.primaryDark || '#1E40AF'};
    }
  }
`;

export const SecondaryButton = styled(Button)`
  &.ant-btn {
    border-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
    color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};

    &:hover, &:focus {
      border-color: ${props => props.theme?.colorPalette?.primaryDark || '#1E40AF'};
      color: ${props => props.theme?.colorPalette?.primaryDark || '#1E40AF'};
    }
  }
`;

export const IconButton = styled(Button)`
  &.ant-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .anticon {
      font-size: ${props => props.size === 'large' ? '18px' : props.size === 'small' ? '12px' : '14px'};
    }
  }
`;

// Forms
export const FormGroup = styled.div`
  margin-bottom: ${props => props.theme?.spacing?.md || 16}px;
`;

export const StyledInput = styled(Input)`
  &.ant-input {
    border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
    border-color: ${props => props.theme?.colorPalette?.border || '#D1D5DB'};

    &:hover {
      border-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
    }

    &:focus {
      border-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
      box-shadow: 0 0 0 2px ${props => props.theme?.colorPalette?.primaryLight || 'rgba(37, 99, 235, 0.2)'};
    }
  }
`;

export const StyledTextArea = styled(Input.TextArea)`
  &.ant-input {
    border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
    border-color: ${props => {
    if (props.theme?.colorPalette?.border) return props.theme.colorPalette.border;
    if (props.theme?.colors?.neutral?.[300]) return props.theme.colors.neutral[300];
    return '#D1D5DB';
  }};

    &:hover {
      border-color: ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
    }

    &:focus {
      border-color: ${props => {
    if (props.theme?.colorPalette?.primary) return props.theme.colorPalette.primary;
    if (props.theme?.colors?.primary?.main) return props.theme.colors.primary.main;
    if (props.theme?.primaryColor) return props.theme.primaryColor;
    return '#2563EB';
  }};
      box-shadow: 0 0 0 2px ${props => {
    if (props.theme?.colorPalette?.primaryLight) return props.theme.colorPalette.primaryLight;
    if (props.theme?.colors?.primary?.light) return props.theme.colors.primary.light;
    return 'rgba(37, 99, 235, 0.2)';
  }};
    }
  }
`;

// Alerts
export const StyledAlert = styled(Alert)`
  &.ant-alert {
    border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
    margin-bottom: ${props => props.theme?.spacing?.md || 16}px;
  }
`;

// Loading
export const LoadingContainer = styled.div`
  ${flexCenter}
  min-height: ${props => props.fullPage ? '100vh' : '200px'};
  width: 100%;
`;

export const StyledSpin = styled(Spin)`
  .ant-spin-dot-item {
    background-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
  }
`;

// Tags
export const StyledTag = styled(Tag)`
  &.ant-tag {
    border-radius: ${props => props.theme?.borderRadius?.sm || '2px'};
    margin-right: ${props => props.theme?.spacing?.xs || 8}px;
    margin-bottom: ${props => props.theme?.spacing?.xs || 8}px;
  }
`;

export const StatusTag = styled(StyledTag)`
  &.ant-tag {
    background-color: ${props => {
    switch (props.status) {
      case 'success':
        return props.theme?.colorPalette?.successLight || '#D1FAE5';
      case 'warning':
        return props.theme?.colorPalette?.warningLight || '#FEF3C7';
      case 'error':
        return props.theme?.colorPalette?.errorLight || '#FEE2E2';
      case 'info':
      default:
        return props.theme?.colorPalette?.infoLight || '#DBEAFE';
    }
  }};

    color: ${props => {
    switch (props.status) {
      case 'success':
        return props.theme?.colorPalette?.success || '#10B981';
      case 'warning':
        return props.theme?.colorPalette?.warning || '#FBBF24';
      case 'error':
        return props.theme?.colorPalette?.error || '#DC2626';
      case 'info':
      default:
        return props.theme?.colorPalette?.info || '#2563EB';
    }
  }};

    border-color: ${props => {
    switch (props.status) {
      case 'success':
        return props.theme?.colorPalette?.success || '#10B981';
      case 'warning':
        return props.theme?.colorPalette?.warning || '#FBBF24';
      case 'error':
        return props.theme?.colorPalette?.error || '#DC2626';
      case 'info':
      default:
        return props.theme?.colorPalette?.info || '#2563EB';
    }
  }};
  }
`;

// Dividers
export const Divider = styled.hr`
  border: none;
  border-top: 1px solid ${props => props.theme?.colorPalette?.border || '#D1D5DB'};
  margin: ${props => props.theme?.spacing?.md || 16}px 0;
`;

// Badges
export const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 1;
  border-radius: 10px;
  background-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
  color: white;
`;

// Avatars
export const Avatar = styled.div`
  width: ${props => props.size || '40px'};
  height: ${props => props.size || '40px'};
  border-radius: 50%;
  background-color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: ${props => parseInt(props.size || '40', 10) / 2.5}px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

// Tooltips
export const TooltipContent = styled.div`
  padding: ${props => props.theme?.spacing?.xs || 8}px ${props => props.theme?.spacing?.sm || 12}px;
  max-width: 300px;
`;

// Modals
export const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${props => props.theme?.spacing?.sm || 12}px;
  margin-top: ${props => props.theme?.spacing?.md || 16}px;
`;

// Tables
export const TableContainer = styled.div`
  overflow-x: auto;

  .ant-table {
    background-color: ${props => props.theme?.colorPalette?.backgroundSecondary || '#FFFFFF'};
  }

  .ant-table-thead > tr > th {
    background-color: ${props => props.theme?.colorPalette?.backgroundTertiary || '#F9FAFB'};
    color: ${props => props.theme?.colorPalette?.textPrimary || '#111827'};
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid ${props => props.theme?.colorPalette?.border || '#D1D5DB'};
  }

  .ant-table-tbody > tr:hover > td {
    background-color: ${props => props.theme?.colorPalette?.primaryLight || '#DBEAFE'};
  }
`;

// Navigation
export const Breadcrumbs = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: ${props => props.theme?.spacing?.md || 16}px;

  a {
    color: ${props => props.theme?.colorPalette?.textSecondary || '#4B5563'};

    &:hover {
      color: ${props => props.theme?.colorPalette?.primary || '#2563EB'};
    }
  }

  span {
    margin: 0 ${props => props.theme?.spacing?.xs || 8}px;
    color: ${props => props.theme?.colorPalette?.textSecondary || '#4B5563'};
  }
`;

// Animations
export const fadeIn = css`
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  animation: fadeIn ${props => props.theme?.animation?.normal || '0.3s'} ease-in-out;
`;

export const slideIn = css`
  @keyframes slideIn {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  animation: slideIn ${props => props.theme?.animation?.normal || '0.3s'} ease-in-out;
`;

export const pulse = css`
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  animation: pulse ${props => props.theme?.animation?.slow || '0.5s'} ease-in-out infinite;
`;

// Export all components
export default {
  PageTitle,
  SectionTitle,
  SubTitle,
  HighlightedText,
  CodeText,
  Container,
  Section,
  Row,
  Column,
  Grid,
  StyledCard,
  FeatureCard,
  DashboardCard,
  PrimaryButton,
  SecondaryButton,
  IconButton,
  FormGroup,
  StyledInput,
  StyledTextArea,
  StyledAlert,
  LoadingContainer,
  StyledSpin,
  StyledTag,
  StatusTag,
  Divider,
  Badge,
  Avatar,
  TooltipContent,
  ModalFooter,
  TableContainer,
  Breadcrumbs,
};
