import React from 'react';
import { notification, message } from 'antd';
import {
  CheckCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import EnhancedWebSocketClient from './EnhancedWebSocketClient';
import { getWebSocketUrl } from '../utils/websocket';

// Configure notification defaults
notification.config({
  placement: 'topRight',
  duration: 4,
  maxCount: 5
});

export const showSuccess = (message, description = '', options = {}) => {
  notification.success({
    message,
    description,
    icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
    ...options
  });
};

export const showError = (message, description = '', options = {}) => {
  notification.error({
    message,
    description,
    icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
    ...options
  });
};

export const showWarning = (message, description = '', options = {}) => {
  notification.warning({
    message,
    description,
    icon: <WarningOutlined style={{ color: '#faad14' }} />,
    ...options
  });
};

export const showInfo = (message, description = '', options = {}) => {
  notification.info({
    message,
    description,
    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
    ...options
  });
};

export const showNotification = (type, message, description = '', options = {}) => {
  switch (type) {
    case 'success':
      showSuccess(message, description, options);
      break;
    case 'error':
      showError(message, description, options);
      break;
    case 'warning':
      showWarning(message, description, options);
      break;
    case 'info':
    default:
      showInfo(message, description, options);
  }
};

/**
 * Real-time Notification Service Class
 *
 * Handles real-time notifications via WebSocket
 */
class RealTimeNotificationService {
  constructor() {
    this.wsClient = null;
    this.isConnected = false;
    this.userId = null;
    this.subscriptions = new Set();
    this.debug = process.env.NODE_ENV === 'development';
  }

  /**
   * Initialize real-time notifications
   * @param {string} userId - Current user ID
   */
  async initialize(userId) {
    if (this.isConnected && this.userId === userId) {
      return;
    }

    this.userId = userId;

    try {
      this.wsClient = new EnhancedWebSocketClient({
        url: getWebSocketUrl('notifications'),
        autoConnect: true,
        autoReconnect: true,
        debug: this.debug
      });

      this._setupEventListeners();
      this.isConnected = true;

      if (this.debug) {
        console.log('[RealTimeNotificationService] Initialized for user:', userId);
      }
    } catch (error) {
      console.error('[RealTimeNotificationService] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Subscribe to notification types
   * @param {Array|string} types - Notification types
   */
  subscribe(types) {
    if (!Array.isArray(types)) {
      types = [types];
    }
    types.forEach(type => this.subscriptions.add(type));
  }

  /**
   * Show toast message
   * @param {string} content - Message content
   * @param {string} type - Message type
   */
  showMessage(content, type = 'info') {
    switch (type) {
      case 'success':
        message.success(content);
        break;
      case 'error':
        message.error(content);
        break;
      case 'warning':
        message.warning(content);
        break;
      default:
        message.info(content);
    }
  }

  /**
   * Set up WebSocket event listeners
   */
  _setupEventListeners() {
    this.wsClient.addEventListener('message', (data) => {
      this._handleMessage(data);
    });

    this.wsClient.addEventListener('open', () => {
      this.isConnected = true;
      if (this.debug) {
        console.log('[RealTimeNotificationService] Connected');
      }
    });

    this.wsClient.addEventListener('close', () => {
      this.isConnected = false;
      if (this.debug) {
        console.log('[RealTimeNotificationService] Disconnected');
      }
    });
  }

  /**
   * Handle incoming messages
   * @param {Object} data - Message data
   */
  _handleMessage(data) {
    if (data.type === 'notification') {
      this._showRealTimeNotification(data.notification);
    }
  }

  /**
   * Show real-time notification
   * @param {Object} notificationData - Notification data
   */
  _showRealTimeNotification(notificationData) {
    const { type, title, message: msg, description } = notificationData;
    showNotification(type || 'info', title || 'Notification', description || msg);
  }

  /**
   * Disconnect from service
   */
  disconnect() {
    if (this.wsClient) {
      this.wsClient.disconnect();
      this.wsClient = null;
    }
    this.isConnected = false;
    this.userId = null;
    this.subscriptions.clear();
  }
}

// Export singleton instance
export const realTimeNotifications = new RealTimeNotificationService();

