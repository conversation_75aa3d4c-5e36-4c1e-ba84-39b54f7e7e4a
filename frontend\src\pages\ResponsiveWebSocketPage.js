import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentView } from '../redux/reducers/uiReducer';
import { Typography, Input, List, Badge, Tabs, Switch, Space, Card, Button, Row, Col } from 'antd';
import { WifiOutlined, SendOutlined, ClearOutlined, SettingOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import * as S from '../styles/components';
import { useAnnouncer, ScreenReaderOnly } from '../components/accessibility';
import { useTranslation } from 'react-i18next';
import ResponsiveLayout from '../components/layout/ResponsiveLayout';

const { Paragraph, Text } = Typography;
const { TabPane } = Tabs;

// Styled components
const MessageContainer = styled.div`
  height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
  border: 1px solid ${props => props.theme?.colorPalette?.border || '#D1D5DB'};
  padding: 8px;
  border-radius: ${props => props.theme?.borderRadius?.md || '4px'};

  @media (max-width: 576px) {
    height: 250px;
  }
`;

const MessageItem = styled(List.Item)`
  padding: 8px;
  background-color: ${props => {
    switch (props.messageType) {
      case 'sent':
        return props.theme?.colorPalette?.infoLight || '#DBEAFE';
      case 'received':
        return props.theme?.colorPalette?.successLight || '#D1FAE5';
      case 'error':
        return props.theme?.colorPalette?.errorLight || '#FEE2E2';
      default:
        return props.theme?.colorPalette?.warningLight || '#FEF3C7';
    }
  }};
  margin-bottom: 8px;
  border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  
  @media (max-width: 576px) {
    flex-direction: column;
  }
`;

const MessageContent = styled.pre`
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  
  @media (max-width: 576px) {
    font-size: 12px;
  }
`;

const EmptyMessages = styled.div`
  text-align: center;
  color: ${props => props.theme.colorPalette.textSecondary};
  margin-top: 120px;
`;

const SettingItem = styled.div`
  margin-bottom: 16px;
`;

const ResponsiveCard = styled(Card)`
  margin-bottom: 16px;
  
  .ant-card-head {
    @media (max-width: 576px) {
      padding: 0 12px;
    }
  }
  
  .ant-card-body {
    @media (max-width: 576px) {
      padding: 12px;
    }
  }
`;

const ButtonGroup = styled(Space)`
  @media (max-width: 576px) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    
    .ant-btn {
      margin-bottom: 8px;
      width: 100%;
    }
  }
`;

/**
 * ResponsiveWebSocketPage component
 * A responsive version of the WebSocket manager
 */
const ResponsiveWebSocketPage = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { announce } = useAnnouncer();
  const [wsUrl, setWsUrl] = useState('ws://localhost:8000/ws/test/');
  const [connected, setConnected] = useState(false);
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [socket, setSocket] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [autoReconnect, setAutoReconnect] = useState(true);

  // Set current view
  useEffect(() => {
    dispatch(setCurrentView('websocket'));
  }, [dispatch]);

  // Format timestamp
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Connect to WebSocket
  const connect = () => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const ws = new WebSocket(wsUrl);
      setSocket(ws);

      ws.onopen = () => {
        setConnected(true);
        const message = 'Connected to WebSocket server';
        setMessages(prev => [...prev, { type: 'system', text: message, timestamp: new Date() }]);
        announce('WebSocket connection established');
      };

      ws.onmessage = (event) => {
        setMessages(prev => [...prev, { type: 'received', text: event.data, timestamp: new Date() }]);
      };

      ws.onclose = (event) => {
        setConnected(false);
        const message = `Disconnected from server: ${event.reason || 'Connection closed'}`;
        setMessages(prev => [...prev, { type: 'system', text: message, timestamp: new Date() }]);
        announce('WebSocket connection closed');

        // Auto reconnect if enabled
        if (autoReconnect) {
          announce('Attempting to reconnect in 3 seconds');
          setTimeout(() => {
            if (!connected) {
              connect();
            }
          }, 3000);
        }
      };

      ws.onerror = (error) => {
        const message = `WebSocket error: ${error.message || 'Unknown error'}`;
        setMessages(prev => [...prev, { type: 'error', text: message, timestamp: new Date() }]);
        announce('WebSocket error occurred', 'assertive');
      };
    } catch (error) {
      setMessages(prev => [...prev, { type: 'error', text: `Failed to connect: ${error.message}`, timestamp: new Date() }]);
      announce('Failed to connect to WebSocket server', 'assertive');
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (socket) {
      socket.close();
      setSocket(null);
    }
  };

  // Send message
  const sendMessage = () => {
    if (!socket || socket.readyState !== WebSocket.OPEN || !messageText.trim()) {
      announce('Cannot send message: WebSocket is not connected', 'assertive');
      return;
    }

    try {
      socket.send(messageText);

      setMessages(prev => [...prev, { type: 'sent', text: messageText, timestamp: new Date() }]);

      // Clear message input
      setMessageText('');
      announce('Message sent');
    } catch (error) {
      setMessages(prev => [...prev, { type: 'error', text: `Failed to send message: ${error.message}`, timestamp: new Date() }]);
      announce('Failed to send message', 'assertive');
    }
  };

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
    announce('Messages cleared');
  };

  // Clean up WebSocket connection on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.close();
      }
    };
  }, [socket]);

  return (
    <ResponsiveLayout>
      <S.PageTitle level={2}>
        <WifiOutlined style={{ marginRight: '8px' }} />
        {t('webSocket.title')}
      </S.PageTitle>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('webSocket.messages.title')} key="1">
          <ResponsiveCard
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>{t('webSocket.connection.title')}</span>
                <Badge
                  status={connected ? "success" : "error"}
                  text={connected ? t('webSocket.connection.status.connected') : t('webSocket.connection.status.disconnected')}
                />
                <ScreenReaderOnly>
                  {connected ? "WebSocket is connected" : "WebSocket is disconnected"}
                </ScreenReaderOnly>
              </div>
            }
          >
            <S.FormGroup>
              <S.StyledInput
                value={wsUrl}
                onChange={(e) => setWsUrl(e.target.value)}
                placeholder="WebSocket URL"
                addonBefore="URL"
              />
            </S.FormGroup>

            <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
              <Col xs={24} sm={24} md={24}>
                <ButtonGroup>
                  <S.PrimaryButton
                    type="primary"
                    onClick={connect}
                    disabled={connected}
                    aria-label="Connect to WebSocket"
                  >
                    {t('webSocket.connection.actions.connect')}
                  </S.PrimaryButton>

                  <S.SecondaryButton
                    danger
                    onClick={disconnect}
                    disabled={!connected}
                    aria-label="Disconnect from WebSocket"
                  >
                    {t('webSocket.connection.actions.disconnect')}
                  </S.SecondaryButton>

                  <S.IconButton
                    onClick={clearMessages}
                    icon={<ClearOutlined />}
                    aria-label="Clear all messages"
                  >
                    {t('webSocket.connection.actions.clear')}
                  </S.IconButton>
                </ButtonGroup>
              </Col>
            </Row>

            <MessageContainer>
              {messages.length === 0 ? (
                <EmptyMessages>
                  <Text type="secondary">{t('webSocket.messages.empty')}</Text>
                </EmptyMessages>
              ) : (
                <List
                  dataSource={messages}
                  aria-label="WebSocket messages"
                  renderItem={(message) => (
                    <MessageItem messageType={message.type}>
                      <div style={{ width: '100%' }}>
                        <MessageHeader>
                          <Text strong>
                            {message.type === 'sent' ? t('webSocket.messages.types.sent') :
                              message.type === 'received' ? t('webSocket.messages.types.received') :
                                message.type === 'error' ? t('webSocket.messages.types.error') : t('webSocket.messages.types.system')}
                          </Text>
                          <Text type="secondary">{formatTime(message.timestamp)}</Text>
                        </MessageHeader>
                        <MessageContent>
                          {message.text}
                        </MessageContent>
                      </div>
                    </MessageItem>
                  )}
                />
              )}
            </MessageContainer>

            <S.FormGroup>
              <S.StyledTextArea
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                placeholder={t('webSocket.messages.input.placeholder')}
                rows={4}
                disabled={!connected}
              />
            </S.FormGroup>

            <S.PrimaryButton
              type="primary"
              onClick={sendMessage}
              disabled={!connected || !messageText.trim()}
              icon={<SendOutlined />}
              aria-label="Send message"
            >
              {t('webSocket.messages.input.send')}
            </S.PrimaryButton>
          </ResponsiveCard>
        </TabPane>

        <TabPane
          tab={
            <span>
              <SettingOutlined />
              {t('webSocket.settings.title')}
            </span>
          }
          key="2"
        >
          <ResponsiveCard title={t('webSocket.settings.title')}>
            <SettingItem>
              <Text strong>{t('webSocket.settings.autoReconnect.title')}</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Switch
                  checked={autoReconnect}
                  onChange={setAutoReconnect}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  {autoReconnect ? t('webSocket.settings.autoReconnect.enabled') : t('webSocket.settings.autoReconnect.disabled')}
                </Text>
              </div>
            </SettingItem>

            <Paragraph>
              <Text type="secondary">
                {t('webSocket.settings.description')}
              </Text>
            </Paragraph>
          </ResponsiveCard>
        </TabPane>
      </Tabs>
    </ResponsiveLayout>
  );
};

export default ResponsiveWebSocketPage;
