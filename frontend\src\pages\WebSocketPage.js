import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentView } from '../redux/reducers/uiReducer';
import { Typography, Input, List, Badge, Tabs, Switch, Space, InputNumber, Tooltip } from 'antd';
import { WifiOutlined, SendOutlined, ClearOutlined, SettingOutlined, InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import * as S from '../styles/components';
import { useAnnouncer, ScreenReaderOnly } from '../components/accessibility';
import { getWebSocketUrl } from '../config/env';
import EnhancedWebSocketClient, { ConnectionState } from '../services/EnhancedWebSocketClient';

const { Paragraph, Text } = Typography;
const { TabPane } = Tabs;

// Styled components
const MessageContainer = styled.div`
  height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
  border: 1px solid ${props => props.theme?.colorPalette?.border || '#D1D5DB'};
  padding: 8px;
  border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
`;

const MessageItem = styled(List.Item)`
  padding: 8px;
  background-color: ${props => {
    switch (props.messageType) {
      case 'sent':
        return props.theme?.colorPalette?.infoLight || '#DBEAFE';
      case 'received':
        return props.theme?.colorPalette?.successLight || '#D1FAE5';
      case 'error':
        return props.theme?.colorPalette?.errorLight || '#FEE2E2';
      default:
        return props.theme?.colorPalette?.warningLight || '#FEF3C7';
    }
  }};
  margin-bottom: 8px;
  border-radius: ${props => props.theme?.borderRadius?.md || '4px'};
`;

const MessageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
`;

const MessageContent = styled.pre`
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
`;

const EmptyMessages = styled.div`
  text-align: center;
  color: ${props => props.theme?.colorPalette?.textSecondary || '#4B5563'};
  margin-top: 120px;
`;

const SettingItem = styled.div`
  margin-bottom: 16px;
`;

/**
 * WebSocket Page
 * Dedicated page for WebSocket functionality with enhanced WebSocket client
 */
const WebSocketPage = () => {
  const dispatch = useDispatch();
  const { announce } = useAnnouncer();
  const [wsUrl, setWsUrl] = useState(getWebSocketUrl('test'));
  const [connected, setConnected] = useState(false);
  const [connectionState, setConnectionState] = useState(ConnectionState.CLOSED);
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [activeTab, setActiveTab] = useState('1');
  const [isConnecting, setIsConnecting] = useState(false);

  // WebSocket client reference
  const wsClientRef = useRef(null);

  // WebSocket settings
  const [settings, setSettings] = useState({
    autoReconnect: true,
    debug: true,
    batchInterval: 50,
    maxBatchSize: 100,
    heartbeatInterval: 30000,
    enableCompression: true,
    persistOfflineMessages: true,
    reconnectInterval: 3000,
    maxReconnectAttempts: 10
  });

  // Set the current view to 'websocket' when this page loads
  useEffect(() => {
    dispatch(setCurrentView('websocket'));

    // Clean up WebSocket client on unmount
    return () => {
      if (wsClientRef.current) {
        wsClientRef.current.destroy();
        wsClientRef.current = null;
      }
    };
  }, [dispatch]);

  // Connect to WebSocket using EnhancedWebSocketClient
  const connect = () => {
    // Clean up existing client
    if (wsClientRef.current) {
      wsClientRef.current.destroy();
    }

    // Add connection attempt message
    setMessages(prev => [...prev, {
      type: 'system',
      text: `Attempting to connect to ${wsUrl}`,
      timestamp: new Date()
    }]);
    announce(`Attempting to connect to ${wsUrl}`);
    setIsConnecting(true);

    try {
      // Create new enhanced WebSocket client
      wsClientRef.current = new EnhancedWebSocketClient({
        url: wsUrl,
        autoConnect: false,
        debug: settings.debug,
        autoReconnect: settings.autoReconnect,
        reconnectInterval: settings.reconnectInterval,
        maxReconnectAttempts: settings.maxReconnectAttempts,
        batchInterval: settings.batchInterval,
        maxBatchSize: settings.maxBatchSize,
        heartbeatInterval: settings.heartbeatInterval,
        enableCompression: settings.enableCompression,
        persistOfflineMessages: settings.persistOfflineMessages
      });

      // Add event listeners
      wsClientRef.current.addEventListener('open', (event) => {
        setConnected(true);
        setConnectionState(ConnectionState.OPEN);
        setIsConnecting(false);

        const message = `Connected to WebSocket server at ${wsUrl}`;
        setMessages(prev => [...prev, { type: 'system', text: message, timestamp: new Date() }]);
        announce('WebSocket connection established');

        // Send a test message automatically
        setTimeout(() => {
          if (wsClientRef.current && wsClientRef.current.connectionState === ConnectionState.OPEN) {
            try {
              const testMessage = {
                type: 'ping',
                timestamp: new Date().toISOString(),
                client: 'EnhancedWebSocketPage'
              };

              wsClientRef.current.send(testMessage, true, { urgent: true });

              setMessages(prev => [...prev, {
                type: 'sent',
                text: JSON.stringify(testMessage, null, 2),
                timestamp: new Date(),
                isTest: true
              }]);
              announce('Sent automatic test message');
            } catch (e) {
              console.error('Failed to send test message:', e);
            }
          }
        }, 1000);
      });

      wsClientRef.current.addEventListener('message', (event) => {
        try {
          const { data } = event;
          let displayText;

          if (typeof data === 'string') {
            displayText = data;
          } else {
            displayText = JSON.stringify(data, null, 2);
          }

          setMessages(prev => [...prev, {
            type: 'received',
            text: displayText,
            timestamp: new Date(),
            raw: event.originalEvent?.data
          }]);
          announce('Message received from server');
        } catch (error) {
          setMessages(prev => [...prev, {
            type: 'received',
            text: String(event.data),
            timestamp: new Date(),
            parseError: error.message
          }]);
          announce('Message received but could not be processed');
        }
      });

      wsClientRef.current.addEventListener('close', (event) => {
        setConnected(false);
        setConnectionState(ConnectionState.CLOSED);
        setIsConnecting(false);

        const message = `Disconnected from server: ${event.reason || 'Connection closed'} (code: ${event.code})`;
        setMessages(prev => [...prev, {
          type: 'system',
          text: message,
          timestamp: new Date(),
          wasClean: event.wasClean,
          code: event.code
        }]);
        announce('WebSocket connection closed');
      });

      wsClientRef.current.addEventListener('error', (error) => {
        setIsConnecting(false);
        const message = `WebSocket error: ${error.message || 'Unknown error'}`;
        setMessages(prev => [...prev, {
          type: 'error',
          text: message,
          timestamp: new Date(),
          error: error
        }]);
        announce('WebSocket error occurred', 'assertive');

        // Log detailed error information
        console.error('WebSocket connection error:', {
          url: wsUrl,
          error: error,
          connectionState: error.connectionState,
          timestamp: new Date().toISOString()
        });
      });

      wsClientRef.current.addEventListener('reconnect_attempt', (data) => {
        setConnectionState(ConnectionState.RECONNECTING);

        const message = `Reconnecting to WebSocket server (attempt ${data.attempt})...`;
        setMessages(prev => [...prev, {
          type: 'system',
          text: message,
          timestamp: new Date()
        }]);
        announce(message);
      });

      // Connect
      wsClientRef.current.open();
      setConnectionState(ConnectionState.CONNECTING);

    } catch (error) {
      setIsConnecting(false);
      setMessages(prev => [...prev, {
        type: 'error',
        text: `Failed to connect: ${error.message}`,
        timestamp: new Date(),
        stack: error.stack
      }]);
      announce(`Connection error: ${error.message}`);
      console.error('Failed to create WebSocket:', error);
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (wsClientRef.current) {
      wsClientRef.current.close();
      setConnectionState(ConnectionState.CLOSING);
      announce('Disconnecting from WebSocket server');
    }
  };

  // Send message
  const sendMessage = () => {
    if (!wsClientRef.current || wsClientRef.current.connectionState !== ConnectionState.OPEN || !messageText.trim()) {
      announce('Cannot send message: WebSocket is not connected', 'assertive');
      return;
    }

    try {
      let messageToSend;

      // Try to parse as JSON
      try {
        messageToSend = JSON.parse(messageText);
      } catch (error) {
        // Send as plain text if not valid JSON
        messageToSend = messageText;
      }

      // Send the message
      wsClientRef.current.send(messageToSend);

      // Add to messages list
      setMessages(prev => [...prev, {
        type: 'sent',
        text: typeof messageToSend === 'string' ? messageToSend : JSON.stringify(messageToSend, null, 2),
        timestamp: new Date()
      }]);

      setMessageText('');
      announce('Message sent');
    } catch (error) {
      announce(`Failed to send message: ${error.message}`, 'assertive');
      console.error('Error sending message:', error);
    }
  };

  // Clear messages
  const clearMessages = () => {
    setMessages([]);
    announce('Messages cleared');
  };

  // Format timestamp
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <S.Container>
      <S.PageTitle level={2}>
        <WifiOutlined style={{ marginRight: '8px' }} />
        WebSocket Manager
      </S.PageTitle>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Messages" key="1">
          <S.StyledCard
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>Enhanced WebSocket Connection</span>
                <Badge
                  status={
                    connectionState === ConnectionState.OPEN ? "success" :
                      connectionState === ConnectionState.CONNECTING ? "processing" :
                        connectionState === ConnectionState.RECONNECTING ? "warning" :
                          connectionState === ConnectionState.CLOSING ? "warning" : "error"
                  }
                  text={
                    connectionState === ConnectionState.OPEN ? "Connected" :
                      connectionState === ConnectionState.CONNECTING ? "Connecting" :
                        connectionState === ConnectionState.RECONNECTING ? "Reconnecting" :
                          connectionState === ConnectionState.CLOSING ? "Closing" : "Disconnected"
                  }
                />
                <ScreenReaderOnly>
                  {connectionState === ConnectionState.OPEN ? "WebSocket is connected" :
                    connectionState === ConnectionState.CONNECTING ? "WebSocket is connecting" :
                      connectionState === ConnectionState.RECONNECTING ? "WebSocket is reconnecting" :
                        connectionState === ConnectionState.CLOSING ? "WebSocket is closing" : "WebSocket is disconnected"}
                </ScreenReaderOnly>
              </div>
            }
            extra={
              <Space>
                <S.PrimaryButton
                  type="primary"
                  onClick={connect}
                  disabled={connectionState !== ConnectionState.CLOSED}
                  loading={isConnecting}
                  icon={<ReloadOutlined />}
                  aria-label="Connect to WebSocket"
                >
                  Connect
                </S.PrimaryButton>
                <S.SecondaryButton
                  danger
                  onClick={disconnect}
                  disabled={connectionState === ConnectionState.CLOSED || connectionState === ConnectionState.CLOSING}
                  aria-label="Disconnect from WebSocket"
                >
                  Disconnect
                </S.SecondaryButton>
                <S.IconButton
                  onClick={clearMessages}
                  icon={<ClearOutlined />}
                  aria-label="Clear all messages"
                >
                  Clear
                </S.IconButton>
              </Space>
            }
          >
            <S.FormGroup>
              <S.StyledInput
                value={wsUrl}
                onChange={(e) => setWsUrl(e.target.value)}
                placeholder="WebSocket URL"
                addonBefore="URL"
              />
            </S.FormGroup>

            <MessageContainer>
              {messages.length === 0 ? (
                <EmptyMessages>
                  <Text type="secondary">No messages yet</Text>
                </EmptyMessages>
              ) : (
                <List
                  dataSource={messages}
                  aria-label="WebSocket messages"
                  renderItem={(message) => (
                    <MessageItem messageType={message.type}>
                      <div style={{ width: '100%' }}>
                        <MessageHeader>
                          <Text strong>
                            {message.type === 'sent' ? 'Sent' :
                              message.type === 'received' ? 'Received' :
                                message.type === 'error' ? 'Error' : 'System'}
                          </Text>
                          <Text type="secondary">{formatTime(message.timestamp)}</Text>
                        </MessageHeader>
                        <MessageContent>
                          {message.text}
                        </MessageContent>
                      </div>
                    </MessageItem>
                  )}
                />
              )}
            </MessageContainer>

            <S.FormGroup>
              <S.StyledTextArea
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                placeholder="Enter message to send (JSON or plain text)"
                rows={4}
                disabled={connectionState !== ConnectionState.OPEN}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
              />
            </S.FormGroup>

            <Space>
              <S.PrimaryButton
                type="primary"
                onClick={sendMessage}
                disabled={connectionState !== ConnectionState.OPEN || !messageText.trim()}
                icon={<SendOutlined />}
                aria-label="Send message"
              >
                Send Message
              </S.PrimaryButton>

              <Tooltip title="Send a test ping message">
                <S.SecondaryButton
                  onClick={() => {
                    if (wsClientRef.current && wsClientRef.current.connectionState === ConnectionState.OPEN) {
                      try {
                        const testMessage = {
                          type: 'ping',
                          timestamp: new Date().toISOString(),
                          client: 'EnhancedWebSocketPage'
                        };

                        wsClientRef.current.send(testMessage, true, { urgent: true });

                        setMessages(prev => [...prev, {
                          type: 'sent',
                          text: JSON.stringify(testMessage, null, 2),
                          timestamp: new Date(),
                          isTest: true
                        }]);
                        announce('Sent test ping message');
                      } catch (e) {
                        console.error('Failed to send test message:', e);
                      }
                    }
                  }}
                  disabled={connectionState !== ConnectionState.OPEN}
                  icon={<InfoCircleOutlined />}
                  aria-label="Send test ping message"
                >
                  Send Ping
                </S.SecondaryButton>
              </Tooltip>
            </Space>
          </S.StyledCard>
        </TabPane>

        <TabPane tab="Settings" key="2">
          <S.StyledCard title="Enhanced WebSocket Settings">
            <SettingItem>
              <Text strong>Auto Reconnect</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Switch
                  checked={settings.autoReconnect}
                  onChange={(value) => setSettings({ ...settings, autoReconnect: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  {settings.autoReconnect ? 'Enabled - Will automatically try to reconnect when disconnected' : 'Disabled - Will not automatically reconnect'}
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Debug Mode</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Switch
                  checked={settings.debug}
                  onChange={(value) => setSettings({ ...settings, debug: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  {settings.debug ? 'Enabled - Detailed logs will be shown in the console' : 'Disabled - Minimal logging'}
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Persist Offline Messages</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Switch
                  checked={settings.persistOfflineMessages}
                  onChange={(value) => setSettings({ ...settings, persistOfflineMessages: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  {settings.persistOfflineMessages ? 'Enabled - Messages will be saved when offline' : 'Disabled - Messages may be lost when offline'}
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Enable Compression</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <Switch
                  checked={settings.enableCompression}
                  onChange={(value) => setSettings({ ...settings, enableCompression: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  {settings.enableCompression ? 'Enabled - Large messages will be compressed' : 'Disabled - No compression'}
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Reconnect Interval (ms)</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <InputNumber
                  min={1000}
                  max={10000}
                  step={500}
                  value={settings.reconnectInterval}
                  onChange={(value) => setSettings({ ...settings, reconnectInterval: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  Time to wait before attempting to reconnect
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Max Reconnect Attempts</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <InputNumber
                  min={1}
                  max={50}
                  value={settings.maxReconnectAttempts}
                  onChange={(value) => setSettings({ ...settings, maxReconnectAttempts: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  Maximum number of reconnection attempts
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Batch Interval (ms)</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <InputNumber
                  min={0}
                  max={500}
                  value={settings.batchInterval}
                  onChange={(value) => setSettings({ ...settings, batchInterval: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  Time to wait before sending batched messages (0 to disable batching)
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Max Batch Size</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <InputNumber
                  min={1}
                  max={1000}
                  value={settings.maxBatchSize}
                  onChange={(value) => setSettings({ ...settings, maxBatchSize: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  Maximum number of messages in a batch
                </Text>
              </div>
            </SettingItem>

            <SettingItem>
              <Text strong>Heartbeat Interval (ms)</Text>
              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <InputNumber
                  min={5000}
                  max={60000}
                  step={5000}
                  value={settings.heartbeatInterval}
                  onChange={(value) => setSettings({ ...settings, heartbeatInterval: value })}
                  style={{ marginRight: '8px' }}
                />
                <Text type="secondary">
                  Time between heartbeat messages to keep the connection alive
                </Text>
              </div>
            </SettingItem>

            <Paragraph>
              <Text type="secondary">
                These settings control the behavior of the Enhanced WebSocket client. Changes will take effect on the next connection.
              </Text>
            </Paragraph>
          </S.StyledCard>
        </TabPane>
      </Tabs>
    </S.Container>
  );
};

export default WebSocketPage;
